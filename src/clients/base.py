from __future__ import annotations

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any, Optional


@dataclass
class FetchResult:
    """Container for a client's fetch results."""

    records: list[dict]
    ids: list[str]
    failed_ids: list[str]


class BaseClient(ABC):
    """Abstract base class for provider clients."""

    name: str

    @abstractmethod
    def fetch(
        self,
        *,
        start_date: Optional[str | Any] = None,
        end_date: Optional[str | Any] = None,
        last_days: Optional[int] = None,
        last_month: bool = False,
    ) -> FetchResult:
        """Fetch provider data and return structured results.

        Implementations should not perform I/O beyond HTTP; writing files should
        be orchestrated by higher-level utilities.

        Args:
            start_date, end_date: date | str | None
                If provided, must be ISO-8601 (YYYY-MM-DD) strings or date objects.
            last_days: int | None
                Number of days back from today.
            last_month: bool
                If True, selects the full previous calendar month.

        Returns:
            FetchResult with records, IDs, and failed IDs.
        """
        raise NotImplementedError
