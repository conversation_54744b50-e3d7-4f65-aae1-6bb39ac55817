from __future__ import annotations

import re

from bs4 import BeautifulSoup


ID_REGEX = re.compile(r"\((.*?)\)")


def _text(el) -> str:
    """Extract text from an element, joining lines and trimming whitespace."""
    return (el.get_text("\n", strip=True) if el else "").strip()


def parse_listing_for_ids(html: str) -> list[str]:
    """Parse the listing page and extract internal IDs enclosed in parentheses.

    Mirrors the Selenium/XPath approach in the notebook by selecting the
    equivalent '#maincontent > div > div:nth-of-type(3) > div:nth-of-type(2)'
    container and applying a regex for '(...)'. If that fails, falls back to a
    broader search within '#maincontent'.
    """
    soup = BeautifulSoup(html, "html.parser")

    # Primary container, equivalent to the original XPath
    container = soup.select_one(
        "#maincontent > div > div:nth-of-type(3) > div:nth-of-type(2)"
    )
    text = _text(container)
    ids = ID_REGEX.findall(text)
    if ids:
        return ids

    # Fallback: scan the whole maincontent area
    main = soup.select_one("#maincontent")
    if main:
        found = ID_REGEX.findall(_text(main))
        if found:
            return found
    # Last resort: scan entire document text (may include false positives)
    return ID_REGEX.findall(_text(soup))


def parse_detail(html: str, oid: str) -> dict:
    """Parse a detail page into a record matching the original notebook logic.

    - Company name from <h1>, trimmed at '(oid'.
    - Organisational data from '#maincontent > div > section:nth-of-type(1)'.
      The notebook zipped orgdata[1::2] with orgdata[2::2] after splitting lines.
    - Person data from '#maincontent > div > section:nth-of-type(2)'.
    """
    soup = BeautifulSoup(html, "html.parser")

    # Company name
    h1 = soup.find("h1")
    company_raw = _text(h1)
    company_name = company_raw.split(f"({oid}")[0].strip() if company_raw else ""

    # Org data
    org_sec = soup.select_one("#maincontent > div > section:nth-of-type(1)")
    org_lines = _text(org_sec).split("\n") if org_sec else []
    # Replicate: keys at 1,3,5,... values at 2,4,6,... (skip title at 0 if present)
    keys = org_lines[1::2]
    vals = org_lines[2::2]
    org = dict(zip(keys, vals))

    # Person data
    per_sec = soup.select_one("#maincontent > div > section:nth-of-type(2)")
    person_text = _text(per_sec)
    if person_text:
        lines = person_text.split("\n")
        if lines and lines[0].strip().lower().startswith("personen"):
            person_text = "\n".join(lines[1:]).strip()

    record = {**org}
    record["Unternehmen"] = company_name
    record["Personendaten"] = person_text
    return record
