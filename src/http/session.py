from __future__ import annotations

from typing import Optional

import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry


def build_session(
    *,
    user_agent: Optional[str] = None,
    total_retries: int = 3,
    backoff_factor: float = 0.3,
    timeout_seconds: float = 10.0,
) -> requests.Session:
    """Create a requests.Session with sane defaults and retries.

    Args:
        user_agent: Optional[str]
            Custom User-Agent header value; if not provided, a generic one is used.
        total_retries: int
            Total number of retries for transient errors.
        backoff_factor: float
            Exponential backoff factor used by urllib3 Retry.
        timeout_seconds: float
            Default timeout applied via a request hook.

    Returns:
        requests.Session
            Configured session with retries and standard headers.
    """
    session = requests.Session()

    # Headers
    session.headers.update(
        {
            "User-Agent": user_agent
            or "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120 Safari/537.36",
            "Accept-Language": "de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7",
        }
    )

    # Retries for idempotent methods
    retry = Retry(
        total=total_retries,
        read=total_retries,
        connect=total_retries,
        backoff_factor=backoff_factor,
        status_forcelist=(500, 502, 503, 504),
        allowed_methods=(
            "GET",
            "HEAD",
        ),
        raise_on_status=False,
        respect_retry_after_header=True,
    )
    adapter = HTTPAdapter(max_retries=retry)
    session.mount("http://", adapter)
    session.mount("https://", adapter)

    # Add a default timeout by wrapping session.request
    _orig_request = session.request

    def _request_with_timeout(method, url, **kwargs):
        if "timeout" not in kwargs:
            kwargs["timeout"] = timeout_seconds
        return _orig_request(method, url, **kwargs)

    session.request = _request_with_timeout  # type: ignore

    return session
