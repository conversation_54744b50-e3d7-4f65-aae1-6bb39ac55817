from __future__ import annotations

from dataclasses import dataclass
from datetime import date, datetime, timedelta
from typing import Optional, Tuple


@dataclass(frozen=True)
class DateRange:
    """Resolved date range used by crawling clients.

    Args:
        start: date
            Inclusive start date.
        end: date
            Inclusive end date.
    """

    start: date
    end: date

    def to_tuple(self) -> <PERSON><PERSON>[date, date]:
        return (self.start, self.end)


def _parse_date(d: date | str) -> date:
    """Parse date from string or return as-is."""
    if isinstance(d, date):
        return d
    return datetime.strptime(d, "%Y-%m-%d").date()


def resolve_date_range(
    *,
    start_date: Optional[date | str] = None,
    end_date: Optional[date | str] = None,
    last_days: Optional[int] = None,
    last_month: bool = False,
    today: Optional[date] = None,
) -> DateRange:
    """Resolve supported date arguments into an inclusive DateRange.

    Supported combinations:
        - start_date + end_date
        - last_days
        - last_month=True

    Rules:
        - If multiple options are provided, raise ValueError.
        - last_days means [today - last_days, today] inclusive.
        - last_month means the full previous calendar month relative to today.

    Args:
        start_date, end_date: date | str | None
            If provided, must be ISO-8601 (YYYY-MM-DD) strings or date objects.
        last_days: int | None
            Number of days back from today.
        last_month: bool
            If True, selects the full previous calendar month.
        today: date | None
            For deterministic testing: override the notion of "today". Defaults to date.today().
    """
    t = today or date.today()

    # Raise error when multiple combinations for `resolve_date_range` are provided
    combos = sum(
        [
            1 if (start_date or end_date) else 0,
            1 if last_days is not None else 0,
            1 if last_month else 0,
        ]
    )
    if combos != 1:
        raise ValueError(
            "Provide exactly one of: (start_date/end_date), last_days, or last_month=True"
        )

    # If last_days is provided, select the range [today - last_days, today] inclusive
    if last_days is not None:
        if last_days < 0:
            raise ValueError("last_days must be non-negative")
        start = t - timedelta(days=last_days)
        end = t
        return DateRange(start, end)

    # If last_month is True, select the full previous calendar month
    if last_month:
        first_of_this_month = t.replace(day=1)
        end_prev_month = first_of_this_month - timedelta(days=1)
        start_prev_month = end_prev_month.replace(day=1)
        return DateRange(start_prev_month, end_prev_month)

    # If start_date is not provided, raise error
    if start_date is None:
        raise ValueError("start_date must be provided")
    s = _parse_date(start_date)

    # If end_date is not provided, use today
    if end_date is None:
        end_date = t
    e = _parse_date(end_date)

    # Validate start <= end
    if s > e:
        raise ValueError("start_date must be <= end_date")

    return DateRange(s, e)
