from datetime import date

import pytest

from src.utils.date_range import resolve_date_range


def test_last_days_basic():
    today = date(2025, 11, 5)
    dr = resolve_date_range(last_days=10, today=today)
    assert dr.start == date(2025, 10, 26)
    assert dr.end == today


def test_last_month_previous_calendar_month():
    today = date(2025, 11, 5)
    dr = resolve_date_range(last_month=True, today=today)
    assert dr.start == date(2025, 10, 1)
    assert dr.end == date(2025, 10, 31)


def test_explicit_range_ok():
    dr = resolve_date_range(start_date="2025-10-01", end_date="2025-10-31")
    assert dr.start == date(2025, 10, 1)
    assert dr.end == date(2025, 10, 31)


def test_invalid_combinations():
    with pytest.raises(ValueError):
        resolve_date_range()
    with pytest.raises(ValueError):
        resolve_date_range(end_date="2025-10-31")
    with pytest.raises(ValueError):
        resolve_date_range(start_date="2025-10-01", end_date="2025-09-30")
    with pytest.raises(ValueError):
        resolve_date_range(start_date="2025-10-01", end_date="2025-10-31", last_days=10)
    with pytest.raises(ValueError):
        resolve_date_range(last_days=5, last_month=True)
