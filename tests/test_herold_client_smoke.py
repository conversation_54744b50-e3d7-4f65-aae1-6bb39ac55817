from pathlib import Path

import pandas as pd

from src.clients.herold.client import HeroldClient
from src.clients.herold import sources


class FakeResp:
    def __init__(self, text: str, status_code: int = 200):
        self.text = text
        self.status_code = status_code

    def raise_for_status(self):
        if self.status_code >= 400:
            raise RuntimeError(self.status_code)


class FakeSession:
    def __init__(self, listing_pages: list[str], details_by_id: dict[str, str]):
        self._listing_pages = listing_pages
        self._details_by_id = details_by_id
        self._calls = []

    def get(self, url: str, **kwargs):
        self._calls.append(url)
        # listing pages
        if url.startswith(sources.BASE + "/s?"):
            # page index is 1..N; map to our list; when out of range, return empty listing
            from urllib.parse import parse_qs, urlparse

            qs = parse_qs(urlparse(url).query)
            page = int(qs.get("page", [1])[0])
            if 1 <= page <= len(self._listing_pages):
                return FakeResp(self._listing_pages[page - 1])
            return FakeResp("<html><main id='maincontent'><div></div></main></html>")

        # details
        if url.startswith(sources.BASE + "/f/"):
            oid = url.rsplit("/", 1)[-1]
            html = self._details_by_id.get(oid)
            if html is None:
                return FakeResp("", status_code=404)
            return FakeResp(html)

        return FakeResp("", status_code=404)


def test_client_smoke(tmp_path: Path):
    listing1 = (
        Path(__file__).resolve().parent / "fixtures" / "herold" / "listing.html"
    ).read_text(encoding="utf-8")
    # Empty page 2 (ends pagination)
    listing2 = "<html><main id='maincontent'><div></div></main></html>"
    detail = (
        Path(__file__).resolve().parent / "fixtures" / "herold" / "detail.html"
    ).read_text(encoding="utf-8")

    session = FakeSession(
        listing_pages=[listing1, listing2],
        details_by_id={"ABC123": detail, "DEF456": detail},
    )

    client = HeroldClient(session=session)
    res = client.fetch(start_date="2025-10-01", end_date="2025-10-31")

    assert len(res.ids) == 2
    assert set(res.failed_ids) == set()
    assert len(res.records) == 2

    # Verify Excel output path exists and basic content shape
    from src.utils.io import output_path

    out = output_path(
        start_date=pd.Timestamp("2025-10-01").date(),
        end_date=pd.Timestamp("2025-10-31").date(),
    )
    assert out.exists()
