from pathlib import Path

from src.clients.herold.parsers import parse_listing_for_ids, parse_detail

FIX = Path(__file__).resolve().parent / "fixtures" / "herold"


def read(p: Path) -> str:
    return p.read_text(encoding="utf-8")


def test_parse_listing_ids_dedup_and_order():
    html = read(FIX / "listing.html")
    ids = parse_listing_for_ids(html)
    # Note: parse_listing_for_ids returns raw list; de-dup happens in client
    assert ids == ["ABC123", "DEF456", "ABC123"]


def test_parse_detail_record():
    html = read(FIX / "detail.html")
    rec = parse_detail(html, oid="ABC123")
    assert rec["Unternehmen"] == "ACME GmbH"
    assert rec["Adresse"].startswith("Hauptstra")
    assert rec["Sitz"] == "Wien"
    assert rec["Personendaten"].startswith("<PERSON>")
